#!/bin/bash

# Shopify AI Manager OAuth Setup Script

echo "🏢 Shopify AI Manager OAuth Setup"
echo "================================="
echo ""

# Check if .env file exists
if [ -f ".env" ]; then
    echo "📄 Found existing .env file"
    source .env
else
    echo "📄 Creating new .env file"
    touch .env
fi

echo ""
echo "🔑 Setting up environment variables..."
echo ""

# Function to prompt for variable
prompt_for_var() {
    local var_name=$1
    local description=$2
    local current_value=${!var_name}
    
    if [ -n "$current_value" ]; then
        echo "✅ $var_name is already set"
        return
    fi
    
    echo "📝 $description"
    read -p "Enter $var_name: " value
    
    if [ -n "$value" ]; then
        echo "export $var_name=\"$value\"" >> .env
        export $var_name="$value"
        echo "✅ $var_name set successfully"
    else
        echo "⚠️  $var_name left empty"
    fi
    echo ""
}

# Shopify OAuth credentials
echo "🛍️  Shopify Partners Credentials"
echo "Get these from: https://partners.shopify.com/"
echo ""

prompt_for_var "SHOPIFY_API_KEY" "Your Shopify app's API key (Client ID)"
prompt_for_var "SHOPIFY_API_SECRET" "Your Shopify app's API secret (Client Secret)"

echo ""
echo "🤖 AI Configuration"
echo "Get your key from: https://openrouter.ai/keys"
echo ""

prompt_for_var "OPENROUTER_API_KEY" "Your OpenRouter API key for AI functionality"

echo ""
echo "🔐 Security Configuration"
echo ""

if [ -z "$SECRET_KEY" ]; then
    # Generate a random secret key
    SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_hex(32))")
    echo "export SECRET_KEY=\"$SECRET_KEY\"" >> .env
    export SECRET_KEY="$SECRET_KEY"
    echo "✅ Generated random SECRET_KEY"
else
    echo "✅ SECRET_KEY is already set"
fi

echo ""
echo "📋 Configuration Summary:"
echo "========================"

if [ -n "$SHOPIFY_API_KEY" ]; then
    echo "✅ SHOPIFY_API_KEY: ${SHOPIFY_API_KEY:0:8}..."
else
    echo "❌ SHOPIFY_API_KEY: Not set"
fi

if [ -n "$SHOPIFY_API_SECRET" ]; then
    echo "✅ SHOPIFY_API_SECRET: ${SHOPIFY_API_SECRET:0:8}..."
else
    echo "❌ SHOPIFY_API_SECRET: Not set"
fi

if [ -n "$OPENROUTER_API_KEY" ]; then
    echo "✅ OPENROUTER_API_KEY: ${OPENROUTER_API_KEY:0:8}..."
else
    echo "❌ OPENROUTER_API_KEY: Not set"
fi

if [ -n "$SECRET_KEY" ]; then
    echo "✅ SECRET_KEY: Generated"
else
    echo "❌ SECRET_KEY: Not set"
fi

echo ""
echo "🚀 Next Steps:"
echo "=============="

if [ -n "$SHOPIFY_API_KEY" ] && [ -n "$SHOPIFY_API_SECRET" ]; then
    echo "1. ✅ OAuth credentials are configured"
    echo "2. 🌐 Start the app: source .env && python oauth_app.py"
    echo "3. 📱 Install on store: http://localhost:5000/auth/install"
else
    echo "1. 🏢 Create a Shopify Partners app at: https://partners.shopify.com/"
    echo "2. 🔑 Get your API key and secret from the app settings"
    echo "3. 🔄 Run this script again to set the credentials"
    echo "4. 🌐 Then start the app: source .env && python oauth_app.py"
fi

if [ -z "$OPENROUTER_API_KEY" ]; then
    echo ""
    echo "⚠️  AI features will be limited without OpenRouter API key"
    echo "   Get your key from: https://openrouter.ai/keys"
fi

echo ""
echo "📚 Documentation:"
echo "   OAuth Setup Guide: ./OAUTH_SETUP.md"
echo "   General README: ./README.md"
echo ""

# Make .env file readable only by owner
chmod 600 .env

echo "🔒 Environment file secured (.env)"
echo "✨ Setup complete!"
