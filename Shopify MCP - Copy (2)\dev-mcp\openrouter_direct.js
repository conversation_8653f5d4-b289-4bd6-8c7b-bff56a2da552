#!/usr/bin/env node

/**
 * Direct OpenRouter integration without MCP HTTP wrapper
 * This approach uses OpenRouter directly and provides Shopify context manually
 */

async function queryOpenRouter(
  messages,
  apiKey,
  model = "google/gemini-2.5-pro-preview",
) {
  const response = await fetch(
    "https://openrouter.ai/api/v1/chat/completions",
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
        "X-Title": "Shopify Development Assistant",
      },
      body: JSON.stringify({
        model,
        messages,
        max_tokens: 4000,
      }),
    },
  );

  if (!response.ok) {
    throw new Error(`OpenRouter API error: ${response.statusText}`);
  }

  const data = await response.json();
  return data.choices[0].message.content;
}

async function getShopifyContext(query) {
  // Basic Shopify context - in a real implementation, this would fetch from the MCP server
  const shopifyContext = {
    product: `
Shopify Product API Context:
- Products are created using the Admin GraphQL API
- Key fields: title, description, vendor, product_type, tags
- Products can have multiple variants with different prices, SKUs, inventory
- Use the productCreate mutation to create products
- Use the products query to fetch existing products

Example GraphQL mutation:
mutation productCreate($input: ProductInput!) {
  productCreate(input: $input) {
    product {
      id
      title
      handle
    }
    userErrors {
      field
      message
    }
  }
}
`,
    order: `
Shopify Order API Context:
- Orders represent customer purchases
- Key fields: line_items, customer, shipping_address, billing_address
- Orders can be fulfilled, cancelled, or refunded
- Use the orders query to fetch orders
- Use fulfillmentCreate to fulfill orders

Example GraphQL query:
query getOrders($first: Int!) {
  orders(first: $first) {
    edges {
      node {
        id
        name
        totalPrice
        customer {
          email
        }
      }
    }
  }
}
`,
    graphql: `
Shopify Admin GraphQL API Context:
- Endpoint: https://your-shop.myshopify.com/admin/api/2024-01/graphql.json
- Requires authentication with access token
- Supports queries, mutations, and subscriptions
- Rate limited: 1000 points per app per shop per minute
- Use introspection to explore the schema
- Common objects: Product, Order, Customer, Collection, Fulfillment
`,
    default: `
Shopify Development Context:
- Shopify provides multiple APIs: Admin API, Storefront API, Partner API
- Admin API is for managing store data (products, orders, customers)
- Storefront API is for building custom storefronts
- Use GraphQL for modern development, REST for legacy support
- Authentication via access tokens or OAuth
- Rate limits apply to all APIs
- Webhooks available for real-time updates
`,
  };

  // Find relevant context based on query keywords
  const lowerQuery = query.toLowerCase();
  if (lowerQuery.includes("product")) return shopifyContext.product;
  if (lowerQuery.includes("order")) return shopifyContext.order;
  if (lowerQuery.includes("graphql")) return shopifyContext.graphql;
  return shopifyContext.default;
}

async function askShopifyQuestion(question, apiKey) {
  console.log(`\n🤔 Question: ${question}`);

  // Get relevant Shopify context
  const context = await getShopifyContext(question);

  // Build messages for OpenRouter
  const messages = [
    {
      role: "system",
      content: `You are a Shopify development expert. Use the provided Shopify context to give accurate, helpful answers about Shopify development.

Shopify Context:
${context}

Always provide practical examples and code snippets when relevant.`,
    },
    {
      role: "user",
      content: question,
    },
  ];

  console.log("🤖 Getting response from OpenRouter...");
  const response = await queryOpenRouter(messages, apiKey);

  console.log(`\n✅ Answer:\n${response}`);
  return response;
}

async function main() {
  const apiKey = process.env.OPENROUTER_API_KEY;

  if (!apiKey) {
    console.error("❌ Please set OPENROUTER_API_KEY environment variable");
    console.log("Get your API key from: https://openrouter.ai/keys");
    console.log('Then run: export OPENROUTER_API_KEY="your_api_key_here"');
    process.exit(1);
  }

  console.log("🚀 Shopify + OpenRouter Direct Integration");
  console.log("This provides Shopify development assistance using OpenRouter");
  console.log("");

  // Example questions
  const questions = [
    "How do I create a product using Shopify's GraphQL API?",
    "What's the difference between Shopify's Admin API and Storefront API?",
    "How do I handle product variants in Shopify?",
    "Show me how to query orders with GraphQL in Shopify",
  ];

  try {
    for (const question of questions) {
      await askShopifyQuestion(question, apiKey);
      console.log("\n" + "=".repeat(80));

      // Small delay between questions
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    console.log("\n🎉 Demo completed!");
    console.log("\n💡 To use the full MCP server with real-time Shopify docs:");
    console.log("   1. Use Claude for Code in VS Code (already configured)");
    console.log("   2. Use Claude Desktop with the provided config files");
    console.log("   3. Use Cursor IDE with MCP support");
  } catch (error) {
    console.error("❌ Error:", error.message);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
