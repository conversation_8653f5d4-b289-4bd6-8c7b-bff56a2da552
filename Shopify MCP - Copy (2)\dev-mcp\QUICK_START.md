# 🚀 Quick Start Guide - Shopify MCP for 9ighxj-ir.myshopify.com

## ⚡ Fast Setup (5 minutes)

### 1. Get Your Access Token
I've opened your Shopify admin in the browser. Follow these steps:

1. **In the browser tab that just opened:**
   - Click **"Develop apps"** 
   - If you see "App development is disabled", click **"Enable app development"**
   - Click **"Create an app"**
   - Name: `Shopify MCP Server`
   - Click **"Create app"**

2. **Configure Permissions:**
   - Click **"Configure Admin API scopes"**
   - Enable these scopes:
     - ✅ `read_products`
     - ✅ `write_products` 
     - ✅ `read_orders`
     - ✅ `read_customers`
     - ✅ `read_analytics`
     - ✅ `read_content`
     - ✅ `write_content`
   - Click **"Save"**

3. **Install & Get Token:**
   - Click **"Install app"**
   - Click **"Install app"** again to confirm
   - Copy the **Admin API access token** (you can only see it once!)

### 2. Configure Your Environment

Run the interactive setup:
```bash
npm run setup-store
```

Or manually add to your `.env` file:
```bash
SHOPIFY_STORE_DOMAIN="9ighxj-ir.myshopify.com"
SHOPIFY_ACCESS_TOKEN="your_token_here"
```

### 3. Test Your Connection

```bash
npm run test-api
```

### 4. Build & Start MCP Server

```bash
npm run build
node dist/index.js
```

## 🛠️ Available Commands

| Command | Description |
|---------|-------------|
| `npm run setup-store` | Interactive setup for your store |
| `npm run test-api` | Test API connection |
| `npm run build` | Build the MCP server |
| `npm run inspector` | Open MCP inspector tool |
| `npm run openrouter` | Use with OpenRouter AI |

## 🔧 MCP Integration Options

### Option 1: Claude Desktop
Add to your Claude Desktop config:
```json
{
  "mcpServers": {
    "shopify-dev-mcp": {
      "command": "node",
      "args": ["C:/Users/<USER>/Shopify MCP - Copy (2)/dev-mcp/dist/index.js"],
      "env": {
        "OPT_OUT_INSTRUMENTATION": "true"
      }
    }
  }
}
```

### Option 2: Cursor IDE
Add to your Cursor MCP config:
```json
{
  "mcpServers": {
    "shopify-dev-mcp": {
      "command": "npx",
      "args": ["-y", "@shopify/dev-mcp@latest"]
    }
  }
}
```

### Option 3: OpenRouter (AI Chat)
```bash
npm run openrouter
```

## 🎯 What You Can Do

Once set up, you can:

- **Search Shopify docs**: "How do I create a product with GraphQL?"
- **Get schema info**: "Show me the Product type fields"
- **API guidance**: "Help me write a mutation to update inventory"
- **Best practices**: "What's the recommended way to handle webhooks?"

## 🆘 Troubleshooting

### "App development is disabled"
- Go to Settings → Apps and sales channels
- Click "Develop apps" 
- Enable custom app development

### "Invalid API credentials"
- Check your access token is correctly copied
- Verify the token in your .env file
- Run `npm run test-api` to verify

### "Permission denied"
- Make sure you enabled all required scopes
- Reinstall the app if you added new scopes

### "MCP server not starting"
- Run `npm run build` first
- Check Node.js is installed: `node --version`
- Check the logs for error messages

## 📚 Documentation

- **Full Setup Guide**: `STORE_API_SETUP.md`
- **MCP Instructions**: `MCP_SETUP_INSTRUCTIONS.md`
- **Main README**: `README.md`

## 🔐 Security Notes

- ⚠️ Never share your access token publicly
- 🔒 Store tokens in environment variables only
- 🔄 Rotate credentials regularly
- 👀 Monitor API usage in Partners dashboard

---

**Need Help?** 
- Run `npm run test-api` to diagnose issues
- Check the full setup guide in `STORE_API_SETUP.md`
- Review logs when running `node dist/index.js`
