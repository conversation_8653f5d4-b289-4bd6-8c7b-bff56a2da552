#!/usr/bin/env python3
"""
MCP Client Test Tool
Tests MCP server functionality directly
"""

import json
import subprocess
import sys
import time
from pathlib import Path

class MCPTester:
    def __init__(self):
        self.mcp_path = Path("dist/index.js")
        
    def send_mcp_request(self, request):
        """Send a request to MCP server via stdio"""
        try:
            # Start MCP server process
            process = subprocess.Popen(
                ["node", str(self.mcp_path)],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Send request
            request_json = json.dumps(request) + "\n"
            stdout, stderr = process.communicate(input=request_json, timeout=10)
            
            if stderr:
                print(f"STDERR: {stderr}")
                
            # Parse response
            lines = stdout.strip().split('\n')
            for line in lines:
                if line.startswith('{'):
                    try:
                        return json.loads(line)
                    except json.JSONDecodeError:
                        continue
                        
            return {"error": "No valid JSON response", "stdout": stdout}
            
        except subprocess.TimeoutExpired:
            process.kill()
            return {"error": "Request timeout"}
        except Exception as e:
            return {"error": str(e)}
    
    def test_initialize(self):
        """Test MCP server initialization"""
        print("🔌 Testing MCP Server Initialization...")
        
        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "clientInfo": {
                    "name": "test-client",
                    "version": "1.0.0"
                }
            }
        }
        
        response = self.send_mcp_request(request)
        print(f"Response: {json.dumps(response, indent=2)}")
        return response
    
    def test_list_tools(self):
        """Test listing available tools"""
        print("\n🛠️  Testing List Tools...")
        
        # First initialize
        init_response = self.test_initialize()
        if "error" in init_response:
            print("❌ Initialization failed")
            return
            
        request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/list"
        }
        
        response = self.send_mcp_request(request)
        print(f"Available tools: {json.dumps(response, indent=2)}")
        return response
    
    def test_get_started_tool(self):
        """Test the get_started tool"""
        print("\n🚀 Testing get_started Tool...")

        request = {
            "jsonrpc": "2.0",
            "id": 3,
            "method": "tools/call",
            "params": {
                "name": "get_started",
                "arguments": {
                    "api": "admin"
                }
            }
        }

        response = self.send_mcp_request(request)
        print(f"get_started response: {json.dumps(response, indent=2)}")
        return response

    def test_search_dev_docs(self):
        """Test documentation search"""
        print("\n📚 Testing search_dev_docs with 'products'...")

        request = {
            "jsonrpc": "2.0",
            "id": 4,
            "method": "tools/call",
            "params": {
                "name": "search_dev_docs",
                "arguments": {
                    "prompt": "products"
                }
            }
        }

        response = self.send_mcp_request(request)
        print(f"search_dev_docs response: {json.dumps(response, indent=2)}")
        return response

    def test_introspect_admin_schema(self):
        """Test GraphQL schema introspection"""
        print("\n🔍 Testing introspect_admin_schema...")

        request = {
            "jsonrpc": "2.0",
            "id": 5,
            "method": "tools/call",
            "params": {
                "name": "introspect_admin_schema",
                "arguments": {
                    "query": "product"
                }
            }
        }

        response = self.send_mcp_request(request)
        print(f"introspect_admin_schema response: {json.dumps(response, indent=2)}")
        return response
    
    def test_partners_search(self):
        """Test partner-specific documentation search"""
        print("\n🏢 Testing Partners Documentation Search...")

        request = {
            "jsonrpc": "2.0",
            "id": 6,
            "method": "tools/call",
            "params": {
                "name": "search_dev_docs",
                "arguments": {
                    "prompt": "partners api partner dashboard app development"
                }
            }
        }

        response = self.send_mcp_request(request)
        print(f"Partners search response: {json.dumps(response, indent=2)}")
        return response

    def test_app_development_docs(self):
        """Test app development documentation"""
        print("\n📱 Testing App Development Documentation...")

        request = {
            "jsonrpc": "2.0",
            "id": 7,
            "method": "tools/call",
            "params": {
                "name": "search_dev_docs",
                "arguments": {
                    "prompt": "app development oauth authentication partner account"
                }
            }
        }

        response = self.send_mcp_request(request)
        print(f"App development docs response: {json.dumps(response, indent=2)}")
        return response

    def run_all_tests(self):
        """Run all diagnostic tests"""
        print("🧪 MCP Server Diagnostic Tests")
        print("=" * 50)

        tests = [
            ("Initialize", self.test_initialize),
            ("List Tools", self.test_list_tools),
            ("Get Started Tool", self.test_get_started_tool),
            ("Search Dev Docs", self.test_search_dev_docs),
            ("Introspect Admin Schema", self.test_introspect_admin_schema),
            ("Partners Search", self.test_partners_search),
            ("App Development Docs", self.test_app_development_docs)
        ]

        results = {}

        for test_name, test_func in tests:
            try:
                print(f"\n{'='*20} {test_name} {'='*20}")
                result = test_func()
                success = result and "error" not in result
                results[test_name] = success
                print(f"✅ {test_name}: {'PASS' if success else 'FAIL'}")
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
                results[test_name] = False

        print(f"\n{'='*50}")
        print("📊 Test Summary:")
        for test_name, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"  {status} {test_name}")

def main():
    tester = MCPTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
