#!/usr/bin/env node

/**
 * Simple test to debug the API issue
 */

import express from 'express';
import cors from 'cors';

console.log('Starting test API...');

const app = express();
app.use(cors());
app.use(express.json());

app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Test API is working' });
});

const port = 3000;
app.listen(port, () => {
  console.log(`✅ Test API running on http://localhost:${port}`);
});

console.log('Test API setup complete');
