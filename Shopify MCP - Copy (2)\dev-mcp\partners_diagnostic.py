#!/usr/bin/env python3
"""
Shopify Partners Connection Diagnostic Tool
Comprehensive debugging for Partners integration with MCP server
"""

import json
import subprocess
import requests
import os
import sys
from pathlib import Path
from urllib.parse import urlparse

class PartnersDebugger:
    def __init__(self):
        self.mcp_path = Path("dist/index.js")
        self.results = {}
        
    def check_environment(self):
        """Check development environment setup"""
        print("🌍 Environment Check")
        print("=" * 40)
        
        # Node.js version
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            node_version = result.stdout.strip()
            print(f"✅ Node.js: {node_version}")
            self.results['node_version'] = node_version
        except:
            print("❌ Node.js: Not found")
            self.results['node_version'] = None
            
        # NPM version
        try:
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
            npm_version = result.stdout.strip()
            print(f"✅ NPM: {npm_version}")
            self.results['npm_version'] = npm_version
        except:
            print("❌ NPM: Not found")
            self.results['npm_version'] = None
            
        # Shopify CLI
        try:
            result = subprocess.run(['shopify', 'version'], capture_output=True, text=True)
            if result.returncode == 0:
                cli_version = result.stdout.strip()
                print(f"✅ Shopify CLI: {cli_version}")
                self.results['shopify_cli'] = cli_version
            else:
                print("❌ Shopify CLI: Not installed")
                self.results['shopify_cli'] = None
        except:
            print("❌ Shopify CLI: Not found")
            self.results['shopify_cli'] = None
            
        print()
        
    def check_shopify_auth(self):
        """Check Shopify CLI authentication status"""
        print("🔐 Shopify Authentication Check")
        print("=" * 40)
        
        if not self.results.get('shopify_cli'):
            print("❌ Shopify CLI not available - cannot check auth")
            self.results['auth_status'] = 'cli_missing'
            return
            
        try:
            # Check auth status
            result = subprocess.run(['shopify', 'auth', 'status'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ Shopify CLI Authentication:")
                print(result.stdout)
                self.results['auth_status'] = 'authenticated'
                
                # Try to list organizations
                org_result = subprocess.run(['shopify', 'org', 'list'], 
                                          capture_output=True, text=True, timeout=10)
                if org_result.returncode == 0:
                    print("✅ Partner Organizations:")
                    print(org_result.stdout)
                    self.results['organizations'] = org_result.stdout
                else:
                    print("⚠️  Could not list organizations")
                    self.results['organizations'] = None
                    
            else:
                print("❌ Not authenticated with Shopify CLI")
                print(f"Error: {result.stderr}")
                self.results['auth_status'] = 'not_authenticated'
                
        except subprocess.TimeoutExpired:
            print("❌ Shopify CLI command timed out")
            self.results['auth_status'] = 'timeout'
        except Exception as e:
            print(f"❌ Error checking auth: {e}")
            self.results['auth_status'] = 'error'
            
        print()
        
    def test_mcp_partners_integration(self):
        """Test MCP server's Partners integration"""
        print("🔗 MCP Partners Integration Test")
        print("=" * 40)
        
        if not self.mcp_path.exists():
            print("❌ MCP server not found")
            self.results['mcp_partners'] = 'server_missing'
            return
            
        # Test Partners-specific documentation search
        partners_queries = [
            "partner dashboard",
            "partner api",
            "app development",
            "oauth authentication",
            "partner account setup"
        ]
        
        for query in partners_queries:
            print(f"🔍 Testing query: '{query}'")
            success = self._test_mcp_search(query)
            if success:
                print(f"✅ Found documentation for: {query}")
            else:
                print(f"❌ No results for: {query}")
                
        print()
        
    def _test_mcp_search(self, query):
        """Helper to test MCP documentation search"""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": "search_dev_docs",
                    "arguments": {
                        "prompt": query
                    }
                }
            }
            
            process = subprocess.Popen(
                ["node", str(self.mcp_path)],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            request_json = json.dumps(request) + "\n"
            stdout, stderr = process.communicate(input=request_json, timeout=15)
            
            # Look for successful response
            lines = stdout.strip().split('\n')
            for line in lines:
                if line.startswith('{'):
                    try:
                        response = json.loads(line)
                        if 'result' in response and 'content' in response['result']:
                            return True
                    except json.JSONDecodeError:
                        continue
                        
            return False
            
        except Exception as e:
            print(f"   Error testing query '{query}': {e}")
            return False
            
    def check_network_connectivity(self):
        """Check network connectivity to Shopify services"""
        print("🌐 Network Connectivity Check")
        print("=" * 40)
        
        endpoints = [
            "https://partners.shopify.com",
            "https://shopify.dev",
            "https://api.shopify.com",
            "https://accounts.shopify.com"
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, timeout=10)
                if response.status_code == 200:
                    print(f"✅ {endpoint}: Accessible")
                else:
                    print(f"⚠️  {endpoint}: HTTP {response.status_code}")
            except requests.exceptions.Timeout:
                print(f"❌ {endpoint}: Timeout")
            except requests.exceptions.ConnectionError:
                print(f"❌ {endpoint}: Connection Error")
            except Exception as e:
                print(f"❌ {endpoint}: {e}")
                
        print()
        
    def check_oauth_app_integration(self):
        """Check OAuth app integration"""
        print("🔌 OAuth App Integration Check")
        print("=" * 40)
        
        # Check if OAuth app is running
        try:
            response = requests.get("http://localhost:5000/api/store-status", timeout=5)
            if response.status_code == 200:
                print("✅ OAuth app is running")
                data = response.json()
                print(f"   Connected: {data.get('connected', False)}")
                
                # Check MCP status through OAuth app
                mcp_response = requests.get("http://localhost:5000/api/mcp-status", timeout=5)
                if mcp_response.status_code == 200:
                    mcp_data = mcp_response.json()
                    print(f"✅ MCP integration: {mcp_data.get('connected', False)}")
                else:
                    print("❌ MCP status endpoint failed")
            else:
                print("❌ OAuth app not responding")
                
        except requests.exceptions.ConnectionError:
            print("❌ OAuth app not running on port 5000")
        except Exception as e:
            print(f"❌ Error checking OAuth app: {e}")
            
        print()
        
    def generate_setup_recommendations(self):
        """Generate setup recommendations based on findings"""
        print("💡 Setup Recommendations")
        print("=" * 40)
        
        recommendations = []
        
        # Shopify CLI recommendations
        if not self.results.get('shopify_cli'):
            recommendations.append({
                'priority': 'HIGH',
                'action': 'Install Shopify CLI',
                'command': 'npm install -g @shopify/cli @shopify/theme'
            })
            
        if self.results.get('auth_status') == 'not_authenticated':
            recommendations.append({
                'priority': 'HIGH', 
                'action': 'Authenticate with Shopify Partners',
                'command': 'shopify auth login'
            })
            
        # Environment recommendations
        if not self.results.get('node_version'):
            recommendations.append({
                'priority': 'CRITICAL',
                'action': 'Install Node.js',
                'command': 'Download from https://nodejs.org/'
            })
            
        # MCP recommendations
        recommendations.append({
            'priority': 'MEDIUM',
            'action': 'Verify MCP server configuration',
            'command': 'Check mcpServers config in your client'
        })
        
        # Display recommendations
        for rec in recommendations:
            priority_color = {
                'CRITICAL': '🔴',
                'HIGH': '🟡', 
                'MEDIUM': '🟢'
            }
            
            print(f"{priority_color[rec['priority']]} {rec['priority']}: {rec['action']}")
            print(f"   Command: {rec['command']}")
            print()
            
    def run_full_diagnostic(self):
        """Run complete Partners diagnostic"""
        print("🏢 Shopify Partners Connection Diagnostic")
        print("=" * 60)
        print()
        
        # Run all checks
        self.check_environment()
        self.check_shopify_auth()
        self.test_mcp_partners_integration()
        self.check_network_connectivity()
        self.check_oauth_app_integration()
        self.generate_setup_recommendations()
        
        # Summary
        print("📊 Diagnostic Summary")
        print("=" * 40)
        
        status_items = [
            ("Node.js", bool(self.results.get('node_version'))),
            ("Shopify CLI", bool(self.results.get('shopify_cli'))),
            ("Authentication", self.results.get('auth_status') == 'authenticated'),
            ("MCP Server", self.mcp_path.exists()),
            ("OAuth App", True)  # Will be updated based on actual check
        ]
        
        for item, status in status_items:
            icon = "✅" if status else "❌"
            print(f"{icon} {item}")
            
        print()
        print("🎯 Next Steps:")
        print("1. Address any CRITICAL or HIGH priority recommendations")
        print("2. Ensure Shopify CLI is authenticated with Partners")
        print("3. Verify MCP server configuration in your client")
        print("4. Test Partners API access through MCP tools")

def main():
    debugger = PartnersDebugger()
    debugger.run_full_diagnostic()

if __name__ == "__main__":
    main()
