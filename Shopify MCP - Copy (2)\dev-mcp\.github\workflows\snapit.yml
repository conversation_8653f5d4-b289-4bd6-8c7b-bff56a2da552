name: Snapit

on:
  issue_comment:
    types:
      - created

jobs:
  snapit:
    name: <PERSON>nap<PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - uses: actions/setup-node@v3
        name: Setup node.js
        with:
          cache: npm
          node-version-file: ".nvmrc"

      - name: Install dependencies
        run: npm ci
        shell: bash

      - name: Create snapshot
        uses: Shopify/snapit@main
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        with:
          build_script: npm run build
          comment_command: /snapit
