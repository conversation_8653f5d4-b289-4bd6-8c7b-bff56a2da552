{% extends "base.html" %}

{% block title %}Connect Store - Shopify AI Manager{% endblock %}

{% block page_title %}Connect Your Shopify Store{% endblock %}
{% block page_subtitle %}Connect your store to enable AI-powered management{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Connection Form -->
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plug"></i> Store Connection
                </h5>
            </div>
            <div class="card-body">
                <div id="connection-form">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>How to connect your Shopify store:</strong>
                        <ol class="mt-2 mb-0">
                            <li>Go to your Shopify Admin → Apps → Develop apps</li>
                            <li>Create a private app or use an existing one</li>
                            <li>Configure Admin API access tokens with required permissions</li>
                            <li>Copy your store URL and access token below</li>
                        </ol>
                    </div>
                    
                    <form id="connectForm">
                        <div class="mb-3">
                            <label for="storeUrl" class="form-label">
                                <i class="fas fa-store"></i> Store URL
                            </label>
                            <input type="text" class="form-control" id="storeUrl" 
                                   placeholder="your-store.myshopify.com" required>
                            <div class="form-text">
                                Enter your Shopify store URL (e.g., mystore.myshopify.com)
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="accessToken" class="form-label">
                                <i class="fas fa-key"></i> Admin API Access Token
                            </label>
                            <input type="password" class="form-control" id="accessToken" 
                                   placeholder="shpat_..." required>
                            <div class="form-text">
                                Your private app's Admin API access token
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="saveConnection">
                                <label class="form-check-label" for="saveConnection">
                                    Save connection for this session
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-outline-secondary me-md-2" onclick="testConnection()">
                                <i class="fas fa-vial"></i> Test Connection
                            </button>
                            <button type="submit" class="btn btn-shopify">
                                <i class="fas fa-plug"></i> Connect Store
                            </button>
                        </div>
                    </form>
                </div>
                
                <div id="connection-success" style="display: none;">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <strong>Successfully connected!</strong>
                        Your Shopify store is now connected and ready for AI management.
                    </div>
                    
                    <div class="text-center">
                        <button class="btn btn-outline-danger" onclick="disconnectStore()">
                            <i class="fas fa-unlink"></i> Disconnect Store
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-shopify ms-2">
                            <i class="fas fa-tachometer-alt"></i> Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Required Permissions -->
        <div class="card module-card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt"></i> Required Permissions
                </h5>
            </div>
            <div class="card-body">
                <p>Your private app needs the following Admin API permissions:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Read Permissions:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Products</li>
                            <li><i class="fas fa-check text-success"></i> Orders</li>
                            <li><i class="fas fa-check text-success"></i> Customers</li>
                            <li><i class="fas fa-check text-success"></i> Analytics</li>
                            <li><i class="fas fa-check text-success"></i> Online Store</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Write Permissions:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> Products</li>
                            <li><i class="fas fa-check text-success"></i> Online Store</li>
                            <li><i class="fas fa-check text-success"></i> Marketing Events</li>
                            <li><i class="fas fa-check text-success"></i> Content</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Security Note:</strong> Your access token is stored securely and only used to communicate with your Shopify store. We recommend using a private app with minimal required permissions.
                </div>
            </div>
        </div>
        
        <!-- Setup Guide -->
        <div class="card module-card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-book"></i> Setup Guide
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="setupAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#step1">
                                Step 1: Create a Private App
                            </button>
                        </h2>
                        <div id="step1" class="accordion-collapse collapse show" data-bs-parent="#setupAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li>Log in to your Shopify Admin</li>
                                    <li>Go to <strong>Apps</strong> → <strong>Develop apps</strong></li>
                                    <li>Click <strong>Create an app</strong></li>
                                    <li>Enter app name: "AI Manager" and click <strong>Create app</strong></li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#step2">
                                Step 2: Configure API Access
                            </button>
                        </h2>
                        <div id="step2" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li>Click <strong>Configure Admin API scopes</strong></li>
                                    <li>Select the required permissions listed above</li>
                                    <li>Click <strong>Save</strong></li>
                                    <li>Click <strong>Install app</strong></li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#step3">
                                Step 3: Get Access Token
                            </button>
                        </h2>
                        <div id="step3" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li>In your app settings, go to <strong>API credentials</strong></li>
                                    <li>Copy the <strong>Admin API access token</strong></li>
                                    <li>Copy your store URL (e.g., mystore.myshopify.com)</li>
                                    <li>Enter both in the form above</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Handle form submission
    document.getElementById('connectForm').addEventListener('submit', function(e) {
        e.preventDefault();
        connectStore();
    });
    
    // Connect to store
    function connectStore() {
        const storeUrl = document.getElementById('storeUrl').value.trim();
        const accessToken = document.getElementById('accessToken').value.trim();
        
        if (!storeUrl || !accessToken) {
            alert('Please fill in all required fields');
            return;
        }
        
        // Show loading state
        const submitBtn = document.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';
        submitBtn.disabled = true;
        
        fetch('/api/connect-store', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                store_url: storeUrl,
                access_token: accessToken
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success state
                document.getElementById('connection-form').style.display = 'none';
                document.getElementById('connection-success').style.display = 'block';
                
                // Update global connection status
                updateConnectionStatus();
            } else {
                alert('Connection failed: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            alert('Connection error: ' + error.message);
        })
        .finally(() => {
            // Reset button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }
    
    // Test connection
    function testConnection() {
        const storeUrl = document.getElementById('storeUrl').value.trim();
        const accessToken = document.getElementById('accessToken').value.trim();
        
        if (!storeUrl || !accessToken) {
            alert('Please fill in store URL and access token first');
            return;
        }
        
        // For now, just validate format
        if (!storeUrl.includes('.myshopify.com') && !storeUrl.includes('myshopify.com')) {
            alert('Store URL should be in format: your-store.myshopify.com');
            return;
        }
        
        if (!accessToken.startsWith('shpat_')) {
            alert('Access token should start with "shpat_"');
            return;
        }
        
        alert('Connection test passed! Click "Connect Store" to establish the connection.');
    }
    
    // Disconnect store
    function disconnectStore() {
        if (confirm('Are you sure you want to disconnect your store?')) {
            fetch('/api/disconnect-store', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show form again
                        document.getElementById('connection-form').style.display = 'block';
                        document.getElementById('connection-success').style.display = 'none';
                        
                        // Clear form
                        document.getElementById('connectForm').reset();
                        
                        // Update global connection status
                        updateConnectionStatus();
                    }
                })
                .catch(error => {
                    alert('Disconnect error: ' + error.message);
                });
        }
    }
    
    // Check if already connected on page load
    document.addEventListener('DOMContentLoaded', function() {
        fetch('/api/store-status')
            .then(response => response.json())
            .then(data => {
                if (data.connected) {
                    document.getElementById('connection-form').style.display = 'none';
                    document.getElementById('connection-success').style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error checking connection status:', error);
            });
    });
</script>
{% endblock %}
