#!/usr/bin/env node

/**
 * Interactive setup script for configuring your Shopify store with MCP
 * Store: 9ighxj-ir.myshopify.com
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createInterface } from 'readline';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const rl = createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function main() {
  console.log('🚀 Shopify Store MCP Setup');
  console.log('Store: 9ighxj-ir.myshopify.com');
  console.log('=' * 50);
  console.log('');
  
  console.log('This script will help you configure your Shopify store for MCP integration.');
  console.log('');
  
  // Check current configuration
  const envPath = path.join(__dirname, 'shopify_ai_manager', '.env');
  let currentConfig = {};
  
  if (fs.existsSync(envPath)) {
    console.log('✅ Found existing .env file');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        currentConfig[key.trim()] = value.trim().replace(/"/g, '');
      }
    });
    
    console.log('Current configuration:');
    console.log(`  SHOPIFY_API_KEY: ${currentConfig.SHOPIFY_API_KEY ? '✅ Set' : '❌ Not set'}`);
    console.log(`  SHOPIFY_API_SECRET: ${currentConfig.SHOPIFY_API_SECRET ? '✅ Set' : '❌ Not set'}`);
    console.log(`  SHOPIFY_STORE_DOMAIN: ${currentConfig.SHOPIFY_STORE_DOMAIN ? '✅ Set' : '❌ Not set'}`);
    console.log(`  SHOPIFY_ACCESS_TOKEN: ${currentConfig.SHOPIFY_ACCESS_TOKEN ? '✅ Set' : '❌ Not set'}`);
    console.log(`  OPENROUTER_API_KEY: ${currentConfig.OPENROUTER_API_KEY ? '✅ Set' : '❌ Not set'}`);
  } else {
    console.log('⚠️  No .env file found. We\'ll create one.');
  }
  
  console.log('');
  
  // Get store access token
  if (!currentConfig.SHOPIFY_ACCESS_TOKEN) {
    console.log('🔑 You need to get your Shopify Admin API access token.');
    console.log('');
    console.log('Steps to get your access token:');
    console.log('1. Go to: https://9ighxj-ir.myshopify.com/admin');
    console.log('2. Navigate to Settings → Apps and sales channels');
    console.log('3. Click "Develop apps"');
    console.log('4. Create a new custom app or use existing one');
    console.log('5. Configure the required scopes (see STORE_API_SETUP.md for details)');
    console.log('6. Install the app');
    console.log('7. Copy the Admin API access token');
    console.log('');
    
    const openBrowser = await question('Would you like me to open your Shopify admin in the browser? (y/n): ');
    if (openBrowser.toLowerCase() === 'y' || openBrowser.toLowerCase() === 'yes') {
      const { spawn } = await import('child_process');
      const url = 'https://9ighxj-ir.myshopify.com/admin/settings/apps';
      
      // Open browser based on platform
      const platform = process.platform;
      let command;
      
      if (platform === 'win32') {
        command = 'start';
      } else if (platform === 'darwin') {
        command = 'open';
      } else {
        command = 'xdg-open';
      }
      
      try {
        spawn(command, [url], { detached: true, stdio: 'ignore' });
        console.log('✅ Opened Shopify admin in your browser');
      } catch (error) {
        console.log('❌ Could not open browser. Please visit manually:');
        console.log(`   ${url}`);
      }
    }
    
    console.log('');
    const accessToken = await question('Enter your Shopify Admin API access token: ');
    
    if (accessToken && accessToken.length > 10) {
      currentConfig.SHOPIFY_ACCESS_TOKEN = accessToken;
      console.log('✅ Access token saved');
    } else {
      console.log('❌ Invalid access token. Please run this script again with a valid token.');
      rl.close();
      return;
    }
  }
  
  // Set store domain
  currentConfig.SHOPIFY_STORE_DOMAIN = '9ighxj-ir.myshopify.com';
  currentConfig.SHOPIFY_API_VERSION = '2024-01';
  
  // Keep existing API credentials
  if (!currentConfig.SHOPIFY_API_KEY) {
    currentConfig.SHOPIFY_API_KEY = 'a7dadc9d25078a098f0d5aabcf055413';
  }
  if (!currentConfig.SHOPIFY_API_SECRET) {
    currentConfig.SHOPIFY_API_SECRET = 'a8084ee0de0188b16a12dfa76e7e9208';
  }
  if (!currentConfig.OPENROUTER_API_KEY) {
    currentConfig.OPENROUTER_API_KEY = 'sk-or-v1-242ac80d4f2b7ce0e75c7f818401443fe235c41b5503ad9df';
  }
  
  // Write updated .env file
  const envContent = Object.entries(currentConfig)
    .map(([key, value]) => `${key}="${value}"`)
    .join('\n') + '\n';
  
  // Ensure directory exists
  const envDir = path.dirname(envPath);
  if (!fs.existsSync(envDir)) {
    fs.mkdirSync(envDir, { recursive: true });
  }
  
  fs.writeFileSync(envPath, envContent);
  console.log('');
  console.log('✅ Configuration saved to .env file');
  
  // Test the connection
  console.log('');
  const testConnection = await question('Would you like to test the API connection now? (y/n): ');
  
  if (testConnection.toLowerCase() === 'y' || testConnection.toLowerCase() === 'yes') {
    console.log('');
    console.log('🧪 Testing API connection...');
    
    try {
      const { spawn } = await import('child_process');
      const testProcess = spawn('node', ['test_store_api.js'], {
        stdio: 'inherit',
        cwd: __dirname
      });
      
      testProcess.on('close', (code) => {
        console.log('');
        if (code === 0) {
          console.log('🎉 Setup complete! Your Shopify store is ready for MCP integration.');
          console.log('');
          console.log('Next steps:');
          console.log('1. Build the MCP server: npm run build');
          console.log('2. Test the MCP server: node dist/index.js');
          console.log('3. Configure your MCP client (Claude Desktop, Cursor, etc.)');
          console.log('4. Read STORE_API_SETUP.md for detailed usage instructions');
        } else {
          console.log('❌ API test failed. Please check your configuration and try again.');
          console.log('See STORE_API_SETUP.md for troubleshooting tips.');
        }
        rl.close();
      });
    } catch (error) {
      console.log('❌ Could not run API test:', error.message);
      console.log('You can test manually by running: npm run test-api');
      rl.close();
    }
  } else {
    console.log('');
    console.log('✅ Setup complete!');
    console.log('');
    console.log('To test your configuration, run: npm run test-api');
    console.log('To build the MCP server, run: npm run build');
    console.log('');
    console.log('See STORE_API_SETUP.md for detailed usage instructions.');
    rl.close();
  }
}

main().catch(error => {
  console.error('❌ Setup failed:', error);
  rl.close();
  process.exit(1);
});
