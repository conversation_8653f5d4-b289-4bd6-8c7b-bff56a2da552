#!/usr/bin/env node

/**
 * Shopify MCP HTTP API Wrapper
 *
 * This creates an HTTP API that wraps the Shopify MCP server,
 * making it easy to use with any LLM provider (OpenRouter, OpenAI, etc.)
 */

import express from "express";
import cors from "cors";
import { spawn } from "child_process";
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";

class ShopifyMCPAPI {
  constructor() {
    this.app = express();
    this.mcpClient = null;
    this.mcpTransport = null;
    this.serverProcess = null;

    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json());

    // Logging middleware
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  setupRoutes() {
    // Health check
    this.app.get("/health", (req, res) => {
      res.json({
        status: "ok",
        mcpConnected: !!this.mcpClient,
        timestamp: new Date().toISOString(),
      });
    });

    // List available tools
    this.app.get("/tools", async (req, res) => {
      try {
        if (!this.mcpClient) {
          return res.status(503).json({ error: "MCP server not connected" });
        }

        const tools = await this.mcpClient.listTools();
        res.json({ tools: tools.tools });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Call a specific tool
    this.app.post("/tools/:toolName", async (req, res) => {
      try {
        if (!this.mcpClient) {
          return res.status(503).json({ error: "MCP server not connected" });
        }

        const { toolName } = req.params;
        const args = req.body || {};

        const result = await this.mcpClient.callTool({
          name: toolName,
          arguments: args,
        });

        res.json({ result });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Search Shopify docs
    this.app.post("/search", async (req, res) => {
      try {
        const { query } = req.body;

        if (!query) {
          return res.status(400).json({ error: "Query is required" });
        }

        const result = await this.mcpClient.callTool({
          name: "search_dev_docs",
          arguments: { query },
        });

        res.json({ result });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // GraphQL schema introspection
    this.app.post("/schema", async (req, res) => {
      try {
        const { query, filter } = req.body;

        const args = {};
        if (query) args.query = query;
        if (filter) args.filter = filter;

        const result = await this.mcpClient.callTool({
          name: "introspect_admin_schema",
          arguments: args,
        });

        res.json({ result });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Get started guide
    this.app.post("/get-started", async (req, res) => {
      try {
        const { api } = req.body;

        const args = {};
        if (api) args.api = api;

        const result = await this.mcpClient.callTool({
          name: "get_started",
          arguments: args,
        });

        res.json({ result });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Fetch docs by path
    this.app.post("/docs", async (req, res) => {
      try {
        const { path } = req.body;

        if (!path) {
          return res.status(400).json({ error: "Path is required" });
        }

        const result = await this.mcpClient.callTool({
          name: "fetch_docs_by_path",
          arguments: { path },
        });

        res.json({ result });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });
  }

  async startMCPServer() {
    console.log("Starting Shopify MCP Server...");

    // Start the MCP server process
    this.serverProcess = spawn("node", ["./dist/index.js"], {
      stdio: ["pipe", "pipe", "pipe"],
      env: {
        ...process.env,
        OPT_OUT_INSTRUMENTATION: "true",
      },
    });

    // Handle server process errors
    this.serverProcess.on("error", (error) => {
      console.error("MCP Server process error:", error);
    });

    this.serverProcess.stderr.on("data", (data) => {
      console.log("MCP Server:", data.toString());
    });

    // Create MCP client and transport
    this.mcpTransport = new StdioClientTransport({
      reader: this.serverProcess.stdout,
      writer: this.serverProcess.stdin,
    });

    this.mcpClient = new Client(
      {
        name: "shopify-mcp-api",
        version: "1.0.0",
      },
      {
        capabilities: {},
      },
    );

    // Connect to the MCP server
    await this.mcpClient.connect(this.mcpTransport);
    console.log("Connected to Shopify MCP Server");
  }

  async start(port = 3000) {
    try {
      await this.startMCPServer();

      this.app.listen(port, () => {
        console.log(`Shopify MCP API running on http://localhost:${port}`);
        console.log("Available endpoints:");
        console.log("  GET  /health - Health check");
        console.log("  GET  /tools - List available tools");
        console.log("  POST /tools/:toolName - Call a specific tool");
        console.log("  POST /search - Search Shopify docs");
        console.log("  POST /schema - GraphQL schema introspection");
        console.log("  POST /get-started - Get started guides");
        console.log("  POST /docs - Fetch docs by path");
      });

      // Graceful shutdown
      process.on("SIGINT", () => {
        console.log("\nShutting down...");
        if (this.serverProcess) {
          this.serverProcess.kill();
        }
        process.exit(0);
      });
    } catch (error) {
      console.error("Failed to start:", error);
      process.exit(1);
    }
  }
}

// Start the API if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const api = new ShopifyMCPAPI();
  const port = process.env.PORT || 3000;
  api.start(port).catch((error) => {
    console.error("❌ Failed to start API:", error);
    process.exit(1);
  });
}

export { ShopifyMCPAPI };
