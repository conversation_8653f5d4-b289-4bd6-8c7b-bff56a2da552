#!/bin/bash

# Quick start script for OpenRouter integration (working version)

echo "🚀 Shopify + OpenRouter Quick Start"
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if OpenRouter API key is set
if [ -z "$OPENROUTER_API_KEY" ]; then
    echo "❌ OPENROUTER_API_KEY environment variable is not set."
    echo ""
    echo "📝 To get started:"
    echo "1. Get your API key from: https://openrouter.ai/keys"
    echo "2. Set it with: export OPENROUTER_API_KEY='your_api_key_here'"
    echo "3. Run this script again"
    echo ""
    exit 1
fi

echo "✅ Node.js found: $(node --version)"
echo "✅ OpenRouter API key is set"
echo ""

# Install dependencies if needed
echo "📦 Installing dependencies..."
npm install

# Build the project if needed
if [ ! -f "dist/index.js" ]; then
    echo "🔨 Building the project..."
    npm run build
fi

echo "✅ Setup complete"
echo ""

# Run the working OpenRouter integration
echo "🤖 Starting Shopify + OpenRouter integration..."
echo "This will demonstrate how to get Shopify development help using OpenRouter"
echo ""

npm run openrouter

echo ""
echo "🎉 Demo completed!"
echo ""
echo "💡 You can now:"
echo "   - Modify simple_openrouter.cjs to ask your own questions"
echo "   - Use different OpenRouter models by changing the model parameter"
echo "   - Integrate this approach into your own applications"
