#!/usr/bin/env node

/**
 * Compare different OpenRouter models for Shopify development
 * Shows how to use different models with the same question
 */

const https = require('https');

function makeRequest(url, options, data) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(body));
        } catch (e) {
          reject(new Error(`Invalid JSON response: ${body}`));
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function queryOpenRouter(messages, apiKey, model) {
  const options = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'X-Title': 'Shopify Model Comparison'
    }
  };

  const data = {
    model,
    messages,
    max_tokens: 2000 // Shorter responses for comparison
  };

  const response = await makeRequest('https://openrouter.ai/api/v1/chat/completions', options, data);
  
  if (response.error) {
    throw new Error(`OpenRouter API error: ${response.error.message}`);
  }

  return response.choices[0].message.content;
}

async function compareModels(question, apiKey) {
  const models = [
    {
      name: 'Google Gemini 2.5 Pro Preview',
      id: 'google/gemini-2.5-pro-preview'
    },
    {
      name: 'Anthropic Claude 3.5 Sonnet',
      id: 'anthropic/claude-3.5-sonnet'
    },
    {
      name: 'OpenAI GPT-4',
      id: 'openai/gpt-4'
    }
  ];

  const shopifyContext = `
Shopify Development Context:
- Products are created using the Admin GraphQL API
- Key fields: title, description, vendor, product_type, tags
- Products can have multiple variants with different prices, SKUs, inventory
- Use the productCreate mutation to create products
`;

  const messages = [
    {
      role: 'system',
      content: `You are a Shopify development expert. Provide a concise, practical answer.

${shopifyContext}`
    },
    {
      role: 'user',
      content: question
    }
  ];

  console.log(`🤔 Question: ${question}`);
  console.log('=' * 80);

  for (const model of models) {
    try {
      console.log(`\n🤖 ${model.name} (${model.id}):`);
      console.log('-'.repeat(50));
      
      const response = await queryOpenRouter(messages, apiKey, model.id);
      console.log(response);
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.log(`❌ Error with ${model.name}: ${error.message}`);
    }
  }
}

async function main() {
  const apiKey = process.env.OPENROUTER_API_KEY;
  
  if (!apiKey) {
    console.error('❌ Please set OPENROUTER_API_KEY environment variable');
    console.log('Get your API key from: https://openrouter.ai/keys');
    console.log('Then run: export OPENROUTER_API_KEY="your_api_key_here"');
    return;
  }

  console.log('🚀 Shopify Development - Model Comparison');
  console.log('Comparing responses from different OpenRouter models');
  console.log('');

  const question = "How do I create a simple product with one variant using Shopify's GraphQL API?";

  try {
    await compareModels(question, apiKey);
    
    console.log('\n🎉 Comparison completed!');
    console.log('\n💡 Tips:');
    console.log('- Gemini 2.5 Pro Preview: Latest Google model with strong reasoning');
    console.log('- Claude 3.5 Sonnet: Excellent for code and detailed explanations');
    console.log('- GPT-4: Well-rounded performance across different tasks');
    console.log('\n🔧 To change the default model, edit the model parameter in simple_openrouter.cjs');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

main().catch(console.error);
