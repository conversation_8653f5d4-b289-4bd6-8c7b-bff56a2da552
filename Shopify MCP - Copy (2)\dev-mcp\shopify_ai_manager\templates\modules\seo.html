{% extends "base.html" %}

{% block title %}SEO Optimization - Shopify AI Manager{% endblock %}

{% block page_title %}SEO Optimization{% endblock %}
{% block page_subtitle %}Improve your store's search rankings with AI-powered SEO{% endblock %}

{% block content %}
<div class="row">
    <!-- <PERSON><PERSON> Chat Assistant -->
    <div class="col-lg-8 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-robot"></i> SEO AI Assistant
                </h5>
            </div>
            <div class="card-body">
                <div class="chat-container">
                    <div class="chat-messages" id="seo-chat-messages">
                        <div class="message ai">
                            <i class="fas fa-search"></i> 
                            <strong>SEO Assistant:</strong> Hello! I'm here to help optimize your Shopify store's SEO. 
                            I can help with:
                            <ul class="mt-2 mb-0">
                                <li>Product title and description optimization</li>
                                <li>Meta tags and structured data</li>
                                <li>Keyword research and analysis</li>
                                <li>Content strategy for better rankings</li>
                                <li>Technical SEO improvements</li>
                            </ul>
                            What would you like to work on today?
                        </div>
                    </div>
                    <div class="chat-input">
                        <div class="input-group">
                            <input type="text" class="form-control" id="seo-chat-input" 
                                   placeholder="Ask about SEO optimization...">
                            <button class="btn btn-shopify" type="button" onclick="sendSEOMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- SEO Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick SEO Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="quickSEOAction('audit')">
                        <i class="fas fa-search"></i> SEO Audit
                    </button>
                    <button class="btn btn-outline-success" onclick="quickSEOAction('keywords')">
                        <i class="fas fa-key"></i> Keyword Research
                    </button>
                    <button class="btn btn-outline-info" onclick="quickSEOAction('meta-tags')">
                        <i class="fas fa-tags"></i> Optimize Meta Tags
                    </button>
                    <button class="btn btn-outline-warning" onclick="quickSEOAction('content')">
                        <i class="fas fa-edit"></i> Content Strategy
                    </button>
                    <button class="btn btn-outline-secondary" onclick="quickSEOAction('technical')">
                        <i class="fas fa-cogs"></i> Technical SEO
                    </button>
                </div>
            </div>
        </div>
        
        <!-- SEO Checklist -->
        <div class="card module-card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-check-square"></i> SEO Checklist
                </h5>
            </div>
            <div class="card-body">
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="seo-check-1">
                    <label class="form-check-label" for="seo-check-1">
                        Optimize product titles
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="seo-check-2">
                    <label class="form-check-label" for="seo-check-2">
                        Add meta descriptions
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="seo-check-3">
                    <label class="form-check-label" for="seo-check-3">
                        Optimize images (alt text)
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="seo-check-4">
                    <label class="form-check-label" for="seo-check-4">
                        Set up structured data
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="seo-check-5">
                    <label class="form-check-label" for="seo-check-5">
                        Create XML sitemap
                    </label>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="seo-check-6">
                    <label class="form-check-label" for="seo-check-6">
                        Optimize page speed
                    </label>
                </div>
            </div>
        </div>
    </div>
    
    <!-- SEO Tools -->
    <div class="col-12 mb-4">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools"></i> SEO Tools
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="fas fa-search fa-2x text-primary mb-2"></i>
                                <h6>Keyword Analyzer</h6>
                                <p class="text-muted small">Analyze keyword density and suggestions</p>
                                <button class="btn btn-sm btn-outline-primary" onclick="openTool('keyword-analyzer')">
                                    Open Tool
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="fas fa-tags fa-2x text-success mb-2"></i>
                                <h6>Meta Tag Generator</h6>
                                <p class="text-muted small">Generate optimized meta tags</p>
                                <button class="btn btn-sm btn-outline-success" onclick="openTool('meta-generator')">
                                    Open Tool
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                                <h6>SEO Score</h6>
                                <p class="text-muted small">Check your store's SEO score</p>
                                <button class="btn btn-sm btn-outline-warning" onclick="openTool('seo-score')">
                                    Check Score
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent SEO Activities -->
    <div class="col-12">
        <div class="card module-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Recent SEO Activities
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Action</th>
                                <th>Target</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody id="seo-activities">
                            <tr>
                                <td><i class="fas fa-search text-primary"></i> Keyword optimization</td>
                                <td>Product: "Wireless Headphones"</td>
                                <td><span class="badge bg-success">Completed</span></td>
                                <td>2 hours ago</td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-tags text-info"></i> Meta description update</td>
                                <td>Homepage</td>
                                <td><span class="badge bg-warning">In Progress</span></td>
                                <td>1 day ago</td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-image text-secondary"></i> Alt text optimization</td>
                                <td>Product images</td>
                                <td><span class="badge bg-success">Completed</span></td>
                                <td>3 days ago</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- SEO Tool Modal -->
<div class="modal fade" id="seoToolModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="seoToolTitle">SEO Tool</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="seoToolContent">
                <!-- Tool content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-shopify" id="seoToolAction">Apply Changes</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Send SEO message
    function sendSEOMessage() {
        const input = document.getElementById('seo-chat-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        const chatContainer = document.querySelector('.chat-container');
        sendAIMessage(message, 'seo', chatContainer);
        
        input.value = '';
    }
    
    // Handle Enter key in chat input
    document.getElementById('seo-chat-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendSEOMessage();
        }
    });
    
    // Quick SEO actions
    function quickSEOAction(action) {
        const prompts = {
            'audit': 'Perform a comprehensive SEO audit of my Shopify store and provide a detailed report with actionable recommendations.',
            'keywords': 'Help me research and identify the best keywords for my Shopify store products. Analyze current keyword performance and suggest improvements.',
            'meta-tags': 'Review and optimize meta titles and descriptions for my store pages and products. Provide specific recommendations.',
            'content': 'Create a content strategy to improve my store\'s SEO. Suggest blog topics, product description improvements, and content optimization.',
            'technical': 'Analyze technical SEO aspects of my Shopify store including site speed, mobile optimization, and structured data.'
        };
        
        const input = document.getElementById('seo-chat-input');
        input.value = prompts[action];
        sendSEOMessage();
    }
    
    // Open SEO tools
    function openTool(tool) {
        const modal = new bootstrap.Modal(document.getElementById('seoToolModal'));
        const title = document.getElementById('seoToolTitle');
        const content = document.getElementById('seoToolContent');
        
        const toolConfigs = {
            'keyword-analyzer': {
                title: 'Keyword Analyzer',
                content: `
                    <div class="mb-3">
                        <label class="form-label">Enter text to analyze:</label>
                        <textarea class="form-control" rows="4" placeholder="Paste your product description or page content here..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Target keywords (comma-separated):</label>
                        <input type="text" class="form-control" placeholder="wireless headphones, bluetooth, audio">
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> The AI will analyze keyword density and provide optimization suggestions.
                    </div>
                `
            },
            'meta-generator': {
                title: 'Meta Tag Generator',
                content: `
                    <div class="mb-3">
                        <label class="form-label">Page/Product Title:</label>
                        <input type="text" class="form-control" placeholder="Enter the title of your page or product">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Primary Keywords:</label>
                        <input type="text" class="form-control" placeholder="main keyword, secondary keyword">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Brief Description:</label>
                        <textarea class="form-control" rows="3" placeholder="Describe what this page/product is about..."></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> AI will generate optimized meta title and description.
                    </div>
                `
            },
            'seo-score': {
                title: 'SEO Score Checker',
                content: `
                    <div class="mb-3">
                        <label class="form-label">Page URL to analyze:</label>
                        <input type="url" class="form-control" placeholder="https://your-store.myshopify.com/products/example">
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> AI will analyze the page and provide an SEO score with improvement suggestions.
                    </div>
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status" style="display: none;">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div id="seo-score-result"></div>
                    </div>
                `
            }
        };
        
        const config = toolConfigs[tool];
        if (config) {
            title.textContent = config.title;
            content.innerHTML = config.content;
            modal.show();
        }
    }
    
    // Initialize SEO module
    document.addEventListener('DOMContentLoaded', function() {
        // Add some sample activities if needed
        console.log('SEO module initialized');
    });
</script>
{% endblock %}
