#!/usr/bin/env python3
"""
MCP Debug Tool for Shopify AI Manager
Tests MCP server connectivity and functionality
"""

import os
import json
import subprocess
import time
import requests
from pathlib import Path

class MCPDebugger:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.mcp_path = self.project_root / "dist" / "index.js"
        self.oauth_app_url = "http://localhost:5000"
        
    def check_mcp_files(self):
        """Check if MCP server files exist"""
        print("🔍 Checking MCP Server Files...")
        print("=" * 40)
        
        files_to_check = [
            "dist/index.js",
            "dist/tools/index.js", 
            "dist/prompts/index.js",
            "package.json"
        ]
        
        for file_path in files_to_check:
            full_path = self.project_root / file_path
            status = "✅ EXISTS" if full_path.exists() else "❌ MISSING"
            print(f"{status} {file_path}")
            
        print()
        
    def test_mcp_server_start(self):
        """Test if MCP server can start"""
        print("🚀 Testing MCP Server Startup...")
        print("=" * 40)
        
        if not self.mcp_path.exists():
            print("❌ MCP server file not found!")
            return False
            
        try:
            # Test server startup with timeout
            process = subprocess.Popen(
                ["node", str(self.mcp_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait a bit for startup
            time.sleep(2)
            
            # Check if process is still running
            if process.poll() is None:
                print("✅ MCP server started successfully")
                process.terminate()
                process.wait()
                return True
            else:
                stdout, stderr = process.communicate()
                print("❌ MCP server failed to start")
                print(f"STDOUT: {stdout}")
                print(f"STDERR: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error starting MCP server: {e}")
            return False
            
    def test_oauth_app_mcp_integration(self):
        """Test MCP integration in OAuth app"""
        print("🔗 Testing OAuth App MCP Integration...")
        print("=" * 40)
        
        try:
            # Test MCP status endpoint
            response = requests.get(f"{self.oauth_app_url}/api/mcp-status", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print("✅ MCP status endpoint working")
                print(f"   Connected: {data.get('connected', False)}")
                print(f"   Path: {data.get('path', 'N/A')}")
                return True
            else:
                print(f"❌ MCP status endpoint failed: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ OAuth app not running on port 5000")
            return False
        except Exception as e:
            print(f"❌ Error testing MCP integration: {e}")
            return False
            
    def test_mcp_tools(self):
        """Test MCP tools functionality"""
        print("🛠️  Testing MCP Tools...")
        print("=" * 40)
        
        try:
            # Check tools directory
            tools_dir = self.project_root / "dist" / "tools"
            if not tools_dir.exists():
                print("❌ Tools directory not found")
                return False
                
            # List available tools
            tools_files = list(tools_dir.glob("*.js"))
            print(f"📁 Found {len(tools_files)} tool files:")
            for tool_file in tools_files:
                print(f"   - {tool_file.name}")
                
            return len(tools_files) > 0
            
        except Exception as e:
            print(f"❌ Error checking MCP tools: {e}")
            return False
            
    def test_node_dependencies(self):
        """Test Node.js dependencies"""
        print("📦 Testing Node.js Dependencies...")
        print("=" * 40)
        
        try:
            # Check if node_modules exists
            node_modules = self.project_root / "node_modules"
            if not node_modules.exists():
                print("❌ node_modules directory not found")
                print("   Run: npm install")
                return False
                
            # Check key dependencies
            key_deps = [
                "@modelcontextprotocol/sdk",
                "typescript"
            ]
            
            for dep in key_deps:
                dep_path = node_modules / dep
                status = "✅" if dep_path.exists() else "❌"
                print(f"{status} {dep}")
                
            return True
            
        except Exception as e:
            print(f"❌ Error checking dependencies: {e}")
            return False
            
    def run_comprehensive_debug(self):
        """Run all debug tests"""
        print("🔧 MCP Comprehensive Debug Report")
        print("=" * 50)
        print()
        
        tests = [
            ("File Check", self.check_mcp_files),
            ("Node Dependencies", self.test_node_dependencies),
            ("MCP Server Startup", self.test_mcp_server_start),
            ("MCP Tools", self.test_mcp_tools),
            ("OAuth App Integration", self.test_oauth_app_mcp_integration)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                results[test_name] = result
                print()
            except Exception as e:
                print(f"❌ {test_name} failed with error: {e}")
                results[test_name] = False
                print()
                
        # Summary
        print("📊 Debug Summary:")
        print("=" * 20)
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
            
        print()
        
        # Recommendations
        failed_tests = [name for name, result in results.items() if not result]
        if failed_tests:
            print("🔧 Recommendations:")
            print("=" * 20)
            
            if "Node Dependencies" in failed_tests:
                print("• Run: npm install")
                
            if "MCP Server Startup" in failed_tests:
                print("• Check Node.js version: node --version")
                print("• Rebuild MCP server: npm run build")
                
            if "OAuth App Integration" in failed_tests:
                print("• Start OAuth app: python oauth_app.py")
                
        else:
            print("🎉 All tests passed! MCP integration is working correctly.")

def main():
    debugger = MCPDebugger()
    debugger.run_comprehensive_debug()

if __name__ == "__main__":
    main()
