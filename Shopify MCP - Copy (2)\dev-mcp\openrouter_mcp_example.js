#!/usr/bin/env node

/**
 * Example: Using Shopify MCP Server with OpenRouter
 * 
 * This script demonstrates how to:
 * 1. Start the Shopify MCP server
 * 2. Connect to it programmatically
 * 3. Use OpenRouter for LLM responses
 * 4. Pass MCP tool results to the LLM
 */

import { spawn } from 'child_process';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

class OpenRouterMCPClient {
  constructor(openRouterApiKey) {
    this.openRouterApiKey = openRouterApiKey;
    this.mcpClient = null;
    this.mcpTransport = null;
  }

  async startMCPServer() {
    console.log('Starting Shopify MCP Server...');
    
    // Start the MCP server process
    const serverProcess = spawn('node', ['./dist/index.js'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        OPT_OUT_INSTRUMENTATION: 'true'
      }
    });

    // Create MCP client and transport
    this.mcpTransport = new StdioClientTransport({
      reader: serverProcess.stdout,
      writer: serverProcess.stdin
    });

    this.mcpClient = new Client(
      {
        name: 'openrouter-mcp-client',
        version: '1.0.0'
      },
      {
        capabilities: {}
      }
    );

    // Connect to the MCP server
    await this.mcpClient.connect(this.mcpTransport);
    console.log('Connected to Shopify MCP Server');

    return serverProcess;
  }

  async listAvailableTools() {
    if (!this.mcpClient) {
      throw new Error('MCP client not connected');
    }

    const tools = await this.mcpClient.listTools();
    return tools.tools;
  }

  async callTool(toolName, args = {}) {
    if (!this.mcpClient) {
      throw new Error('MCP client not connected');
    }

    const result = await this.mcpClient.callTool({
      name: toolName,
      arguments: args
    });

    return result;
  }

  async queryOpenRouter(prompt, toolResults = []) {
    const messages = [
      {
        role: 'user',
        content: prompt
      }
    ];

    // Add tool results to the conversation if available
    if (toolResults.length > 0) {
      messages.push({
        role: 'assistant',
        content: `I found this information using Shopify tools:\n\n${toolResults.map(r => r.content).join('\n\n')}`
      });
    }

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openRouterApiKey}`,
        'Content-Type': 'application/json',
        'X-Title': 'Shopify MCP Integration'
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3.5-sonnet', // or any other model
        messages: messages,
        max_tokens: 4000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  async processQuery(userQuery) {
    console.log(`\nProcessing query: ${userQuery}`);

    // Determine if we need to use MCP tools
    const needsShopifyInfo = userQuery.toLowerCase().includes('shopify') || 
                            userQuery.toLowerCase().includes('graphql') ||
                            userQuery.toLowerCase().includes('admin api');

    let toolResults = [];

    if (needsShopifyInfo) {
      console.log('Fetching Shopify information...');
      
      try {
        // Example: Search Shopify docs
        if (userQuery.toLowerCase().includes('product')) {
          const searchResult = await this.callTool('search_dev_docs', {
            query: 'product creation'
          });
          toolResults.push(searchResult);
        }

        // Example: Get GraphQL schema info
        if (userQuery.toLowerCase().includes('graphql')) {
          const schemaResult = await this.callTool('introspect_admin_schema', {
            query: 'Product'
          });
          toolResults.push(schemaResult);
        }
      } catch (error) {
        console.error('Error calling MCP tools:', error);
      }
    }

    // Get response from OpenRouter
    const response = await this.queryOpenRouter(userQuery, toolResults);
    return response;
  }
}

// Example usage
async function main() {
  const openRouterApiKey = process.env.OPENROUTER_API_KEY;
  
  if (!openRouterApiKey) {
    console.error('Please set OPENROUTER_API_KEY environment variable');
    process.exit(1);
  }

  const client = new OpenRouterMCPClient(openRouterApiKey);
  
  try {
    // Start MCP server
    const serverProcess = await client.startMCPServer();

    // List available tools
    const tools = await client.listAvailableTools();
    console.log('Available Shopify tools:', tools.map(t => t.name));

    // Example queries
    const queries = [
      "How do I create a product using Shopify's GraphQL API?",
      "What are the available fields for the Product type in Shopify?",
      "Show me how to use Shopify Functions"
    ];

    for (const query of queries) {
      const response = await client.processQuery(query);
      console.log(`\nQuery: ${query}`);
      console.log(`Response: ${response}`);
      console.log('-'.repeat(80));
    }

    // Clean up
    serverProcess.kill();
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { OpenRouterMCPClient };
