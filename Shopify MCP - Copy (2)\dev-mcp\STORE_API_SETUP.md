# Shopify Store API Setup Guide
## For Store: 9ighxj-ir.myshopify.com

This guide will help you set up API access for your specific Shopify store and configure the MCP server to work with it.

## Current Configuration Status

✅ **API Credentials Found**: Your `.env` file contains:
- `SHOPIFY_API_KEY`: a7dadc9d25078a098f0d5aabcf055413
- `SHOPIFY_API_SECRET`: a8084ee0de0188b16a12dfa76e7e9208
- `OPENROUTER_API_KEY`: Configured for AI functionality

## Step 1: Create Custom App in Your Store

### 1.1 Access Your Shopify Admin
1. Go to: **https://9ighxj-ir.myshopify.com/admin**
2. Log in with your store credentials

### 1.2 Navigate to Apps and Sales Channels
1. In your Shopify Admin, go to **Settings** (bottom left)
2. Click **Apps and sales channels**
3. Click **Develop apps** (you may need to enable this first)

### 1.3 Enable Custom App Development (if not already enabled)
1. If you see "App development is disabled", click **Enable app development**
2. Click **Enable app development** in the confirmation dialog

### 1.4 Create New Custom App
1. Click **Create an app**
2. Enter app name: `Shopify MCP Server`
3. Enter app handle: `shopify-mcp-server` (or similar)
4. Click **Create app**

## Step 2: Configure API Permissions

### 2.1 Configure Admin API Scopes
Click **Configure Admin API scopes** and enable these permissions:

#### **Required Scopes for MCP Operations:**
- ✅ `read_products` - Read products, variants, and collections
- ✅ `write_products` - Create and modify products
- ✅ `read_orders` - Read order information
- ✅ `read_customers` - Read customer information
- ✅ `read_analytics` - Read analytics and reports
- ✅ `read_content` - Read blog posts and pages
- ✅ `write_content` - Create and modify content
- ✅ `read_inventory` - Read inventory levels
- ✅ `write_inventory` - Modify inventory levels
- ✅ `read_locations` - Read store locations
- ✅ `read_price_rules` - Read discount codes and rules
- ✅ `read_discounts` - Read discount information

#### **Optional Advanced Scopes:**
- `read_themes` - Read theme information
- `write_themes` - Modify themes
- `read_script_tags` - Read script tags
- `write_script_tags` - Create script tags
- `read_shipping` - Read shipping information
- `read_checkouts` - Read checkout information

### 2.2 Save Scope Configuration
1. Click **Save** after selecting the required scopes

## Step 3: Generate Admin API Access Token

### 3.1 Install the App
1. Click **Install app** 
2. Review the permissions and click **Install app**

### 3.2 Get Your Access Token
1. After installation, you'll see the **Admin API access token**
2. Click **Reveal token once** 
3. **IMPORTANT**: Copy this token immediately - you can only see it once!

## Step 4: Update Your Configuration

### 4.1 Add Store-Specific Configuration
Create a new environment file for your store:

```bash
# Store-specific configuration
SHOPIFY_STORE_DOMAIN="9ighxj-ir.myshopify.com"
SHOPIFY_ACCESS_TOKEN="your_access_token_here"
SHOPIFY_API_VERSION="2024-01"
```

### 4.2 Update Your .env File
Add these lines to your existing `.env` file:

```bash
# Existing credentials (keep these)
SHOPIFY_API_KEY="a7dadc9d25078a098f0d5aabcf055413"
SHOPIFY_API_SECRET="a8084ee0de0188b16a12dfa76e7e9208"
OPENROUTER_API_KEY="sk-or-v1-242ac80d4f2b7ce0e75c7f818401443fe235c41b5503ad9df"

# Add these new store-specific settings
SHOPIFY_STORE_DOMAIN="9ighxj-ir.myshopify.com"
SHOPIFY_ACCESS_TOKEN="your_actual_access_token_here"
SHOPIFY_API_VERSION="2024-01"
```

## Step 5: Test API Connection

### 5.1 Test Basic API Access
Run this command to test your API connection:

```bash
npm run test-api
```

### 5.2 Test MCP Server
Start the MCP server and test the connection:

```bash
npm run build
node dist/index.js
```

## Step 6: Use MCP with Your Store

### 6.1 Available MCP Tools
Once configured, you can use these tools with your store:

- **search_dev_docs** - Search Shopify documentation
- **introspect_admin_schema** - Access GraphQL schema
- **fetch_docs_by_path** - Get specific documentation
- **get_started** - Get API guides and examples

### 6.2 Example Usage
```javascript
// Example: Search for product creation documentation
await mcpClient.callTool('search_dev_docs', {
  query: 'create product GraphQL mutation'
});

// Example: Get product schema information
await mcpClient.callTool('introspect_admin_schema', {
  query: 'Product',
  filter: ['types', 'fields']
});
```

## Step 7: OAuth Integration (Optional)

If you want to use the OAuth web interface:

### 7.1 Configure OAuth Redirect URL
In your custom app settings:
1. Go to **App setup** → **URLs**
2. Set **Allowed redirection URL(s)** to: `http://localhost:5000/auth/callback`

### 7.2 Start OAuth Server
```bash
cd shopify_ai_manager
python oauth_app.py
```

Then visit: http://localhost:5000

## Troubleshooting

### Common Issues:

1. **"App development is disabled"**
   - Enable custom app development in Settings → Apps and sales channels

2. **"Invalid API credentials"**
   - Verify your API key and secret match your Partners app
   - Ensure the access token is correctly copied

3. **"Insufficient permissions"**
   - Check that all required scopes are enabled
   - Reinstall the app if you added new scopes

4. **"Store not found"**
   - Verify the store domain is correct: `9ighxj-ir.myshopify.com`
   - Ensure you're logged into the correct store

### Getting Help:

- Check the MCP server logs: `npm run build && node dist/index.js`
- Test API connection: `npm run test-api`
- View available tools: `npm run inspector`

## Next Steps

Once your API access is configured:

1. **Test the MCP integration** with Claude Desktop or Cursor
2. **Explore the available tools** for your development needs
3. **Build custom integrations** using the MCP server
4. **Monitor API usage** in your Shopify Partners dashboard

---

**Important Security Notes:**
- Never share your access tokens publicly
- Store tokens securely in environment variables
- Regularly rotate your API credentials
- Monitor API usage for unusual activity
