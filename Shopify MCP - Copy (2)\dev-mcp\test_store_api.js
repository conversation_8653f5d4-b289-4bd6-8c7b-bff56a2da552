#!/usr/bin/env node

/**
 * Test script to verify Shopify API connection for your store
 * Store: 9ighxj-ir.myshopify.com
 */

import https from 'https';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
function loadEnv() {
  const envPath = path.join(__dirname, 'shopify_ai_manager', '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim().replace(/"/g, '');
      }
    });
    
    console.log('✅ Loaded environment variables from .env file');
  } else {
    console.log('⚠️  No .env file found at:', envPath);
  }
}

// Test API connection
async function testShopifyAPI() {
  console.log('🧪 Testing Shopify API Connection...');
  console.log('=' * 50);
  
  const storeDomain = process.env.SHOPIFY_STORE_DOMAIN || '9ighxj-ir.myshopify.com';
  const accessToken = process.env.SHOPIFY_ACCESS_TOKEN;
  const apiKey = process.env.SHOPIFY_API_KEY;
  
  console.log(`Store Domain: ${storeDomain}`);
  console.log(`API Key: ${apiKey ? apiKey.substring(0, 8) + '...' : 'NOT SET'}`);
  console.log(`Access Token: ${accessToken ? 'SET (' + accessToken.length + ' chars)' : 'NOT SET'}`);
  console.log('');
  
  if (!accessToken) {
    console.log('❌ SHOPIFY_ACCESS_TOKEN not found!');
    console.log('');
    console.log('To get your access token:');
    console.log('1. Go to: https://9ighxj-ir.myshopify.com/admin');
    console.log('2. Settings → Apps and sales channels → Develop apps');
    console.log('3. Create a custom app or use existing one');
    console.log('4. Install the app and copy the Admin API access token');
    console.log('5. Add it to your .env file: SHOPIFY_ACCESS_TOKEN="your_token_here"');
    return false;
  }
  
  // Test 1: Shop information
  console.log('🏪 Test 1: Fetching shop information...');
  try {
    const shopInfo = await makeGraphQLRequest(storeDomain, accessToken, `
      query {
        shop {
          name
          email
          domain
          myshopifyDomain
          plan {
            displayName
          }
          currencyCode
        }
      }
    `);
    
    if (shopInfo.data && shopInfo.data.shop) {
      console.log('✅ Shop info retrieved successfully!');
      console.log(`   Name: ${shopInfo.data.shop.name}`);
      console.log(`   Domain: ${shopInfo.data.shop.domain}`);
      console.log(`   Shopify Domain: ${shopInfo.data.shop.myshopifyDomain}`);
      console.log(`   Plan: ${shopInfo.data.shop.plan.displayName}`);
      console.log(`   Currency: ${shopInfo.data.shop.currencyCode}`);
    } else {
      console.log('❌ Failed to get shop info:', shopInfo);
      return false;
    }
  } catch (error) {
    console.log('❌ Error fetching shop info:', error.message);
    return false;
  }
  
  console.log('');
  
  // Test 2: Products count
  console.log('📦 Test 2: Checking products...');
  try {
    const productsInfo = await makeGraphQLRequest(storeDomain, accessToken, `
      query {
        products(first: 5) {
          edges {
            node {
              id
              title
              handle
              status
            }
          }
          pageInfo {
            hasNextPage
          }
        }
      }
    `);
    
    if (productsInfo.data && productsInfo.data.products) {
      const products = productsInfo.data.products.edges;
      console.log(`✅ Found ${products.length} products (showing first 5)`);
      products.forEach((product, index) => {
        console.log(`   ${index + 1}. ${product.node.title} (${product.node.status})`);
      });
    } else {
      console.log('❌ Failed to get products:', productsInfo);
      return false;
    }
  } catch (error) {
    console.log('❌ Error fetching products:', error.message);
    return false;
  }
  
  console.log('');
  
  // Test 3: API permissions
  console.log('🔐 Test 3: Checking API permissions...');
  try {
    const permissionsTest = await makeGraphQLRequest(storeDomain, accessToken, `
      query {
        shop {
          name
        }
        products(first: 1) {
          edges {
            node {
              id
            }
          }
        }
        orders(first: 1) {
          edges {
            node {
              id
            }
          }
        }
        customers(first: 1) {
          edges {
            node {
              id
            }
          }
        }
      }
    `);
    
    if (permissionsTest.data) {
      console.log('✅ API permissions verified:');
      console.log('   ✅ Shop access: OK');
      console.log('   ✅ Products access: OK');
      console.log('   ✅ Orders access: OK');
      console.log('   ✅ Customers access: OK');
    } else {
      console.log('❌ Permission test failed:', permissionsTest);
      return false;
    }
  } catch (error) {
    console.log('❌ Error testing permissions:', error.message);
    return false;
  }
  
  console.log('');
  console.log('🎉 All tests passed! Your Shopify API connection is working correctly.');
  console.log('');
  console.log('Next steps:');
  console.log('1. Run: npm run build');
  console.log('2. Test MCP server: node dist/index.js');
  console.log('3. Configure your MCP client (Claude Desktop, Cursor, etc.)');
  
  return true;
}

// Make GraphQL request to Shopify
function makeGraphQLRequest(storeDomain, accessToken, query) {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({ query });
    
    const options = {
      hostname: storeDomain,
      port: 443,
      path: '/admin/api/2024-01/graphql.json',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(data),
        'X-Shopify-Access-Token': accessToken
      }
    };
    
    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve(parsed);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.write(data);
    req.end();
  });
}

// Main execution
async function main() {
  console.log('🚀 Shopify Store API Test');
  console.log('Store: 9ighxj-ir.myshopify.com');
  console.log('=' * 50);
  console.log('');
  
  // Load environment variables
  loadEnv();
  
  // Test API connection
  const success = await testShopifyAPI();
  
  if (!success) {
    console.log('');
    console.log('❌ API test failed. Please check your configuration.');
    process.exit(1);
  }
}

// Run the test
main().catch(error => {
  console.error('❌ Test failed with error:', error);
  process.exit(1);
});
