#!/usr/bin/env python3
"""
Shopify AI Manager - OAuth Version
Enhanced version with Shopify Partners OAuth support
"""

import os
# Load environment variables from .env file without external dependencies
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(dotenv_path):
    with open(dotenv_path) as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            if '=' not in line:
                continue
            if line.startswith("export "):
                line = line[len("export "):]
            key, val = line.split('=', 1)
            val = val.strip().strip('\'"')
            os.environ[key] = val
import json
import hmac
import hashlib
import base64
import requests
import subprocess
from urllib.parse import urlencode, parse_qs
from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from flask_session import Session
import uuid
from datetime import datetime

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['SESSION_TYPE'] = 'filesystem'
Session(app)

# Shopify App Configuration
SHOPIFY_API_KEY = os.environ.get('SHOPIFY_API_KEY')
SHOPIFY_API_SECRET = os.environ.get('SHOPIFY_API_SECRET')
SHOPIFY_SCOPES = 'read_products,write_products,read_orders,read_customers,read_analytics,read_content,write_content'
OPENROUTER_API_KEY = os.environ.get('OPENROUTER_API_KEY')

# AI Assistant for handling chat
class AIAssistant:
    """AI Assistant using OpenRouter API"""

    def __init__(self):
        self.api_key = OPENROUTER_API_KEY
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.model = "google/gemini-2.0-flash-exp"

        # System prompts for different modules
        self.system_prompts = {
            'general': "You are a helpful AI assistant for Shopify store management. Provide practical, actionable advice for e-commerce success.",
            'seo': "You are an SEO expert specializing in Shopify stores. Help optimize product listings, meta tags, content, and search rankings.",
            'ads': "You are a digital advertising specialist. Help create effective ad campaigns, optimize targeting, and improve ROAS for Shopify stores.",
            'email': "You are an email marketing expert. Help create engaging email campaigns, automation workflows, and improve customer retention.",
            'support': "You are a customer service expert. Help create chatbots, response templates, and improve customer satisfaction."
        }

    def chat(self, message, module='general'):
        """Send message to AI and get response"""
        if not self.api_key:
            return "AI functionality requires OPENROUTER_API_KEY to be set."

        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": self.system_prompts.get(module, self.system_prompts['general'])
                    },
                    {
                        "role": "user",
                        "content": message
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.7
            }

            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                return f"AI Error: {response.status_code} - {response.text}"

        except Exception as e:
            return f"AI Error: {str(e)}"

# Initialize AI assistant
ai_assistant = AIAssistant()

# MCP Client for Shopify integration
class ShopifyMCPClient:
    """Client for communicating with Shopify MCP server"""

    def __init__(self):
        self.mcp_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'dist', 'index.js')

    def is_available(self):
        """Check if MCP server is available"""
        return os.path.exists(self.mcp_path)

    def get_status(self):
        """Get MCP server status"""
        if self.is_available():
            return {"connected": True, "path": self.mcp_path}
        else:
            return {"connected": False, "error": "MCP server not found"}

    def start_server(self):
        """Start MCP server (placeholder)"""
        # In a real implementation, this would start the MCP server process
        return {"success": True, "message": "MCP server start requested"}

    def stop_server(self):
        """Stop MCP server (placeholder)"""
        # In a real implementation, this would stop the MCP server process
        return {"success": True, "message": "MCP server stop requested"}

# Initialize MCP client
mcp_client = ShopifyMCPClient()

class ShopifyOAuth:
    """Handle Shopify OAuth authentication"""
    
    @staticmethod
    def get_auth_url(shop_domain):
        """Generate Shopify OAuth authorization URL"""
        if not SHOPIFY_API_KEY:
            raise ValueError("SHOPIFY_API_KEY not configured")
        
        # Generate state for security
        state = str(uuid.uuid4())
        session['oauth_state'] = state
        
        params = {
            'client_id': SHOPIFY_API_KEY,
            'scope': SHOPIFY_SCOPES,
            'redirect_uri': url_for('oauth_callback', _external=True),
            'state': state
        }
        
        return f"https://{shop_domain}/admin/oauth/authorize?{urlencode(params)}"
    
    @staticmethod
    def verify_webhook(data, hmac_header):
        """Verify Shopify webhook signature"""
        if not SHOPIFY_API_SECRET:
            return False
        
        calculated_hmac = base64.b64encode(
            hmac.new(
                SHOPIFY_API_SECRET.encode('utf-8'),
                data,
                digestmod=hashlib.sha256
            ).digest()
        ).decode()
        
        return hmac.compare_digest(calculated_hmac, hmac_header)
    
    @staticmethod
    def exchange_code_for_token(shop_domain, code):
        """Exchange authorization code for access token"""
        if not SHOPIFY_API_KEY or not SHOPIFY_API_SECRET:
            raise ValueError("Shopify API credentials not configured")
        
        token_url = f"https://{shop_domain}/admin/oauth/access_token"
        
        data = {
            'client_id': SHOPIFY_API_KEY,
            'client_secret': SHOPIFY_API_SECRET,
            'code': code
        }
        
        response = requests.post(token_url, data=data)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Token exchange failed: {response.text}")

# OAuth Routes
@app.route('/auth/install')
def install_app():
    """Start Shopify app installation"""
    shop = request.args.get('shop')
    
    if not shop:
        return render_template('oauth_install.html')
    
    # Validate shop domain
    if not shop.endswith('.myshopify.com'):
        shop = f"{shop}.myshopify.com"
    
    try:
        auth_url = ShopifyOAuth.get_auth_url(shop)
        return redirect(auth_url)
    except Exception as e:
        return jsonify({"error": str(e)}), 400

@app.route('/auth/callback')
def oauth_callback():
    """Handle OAuth callback from Shopify"""
    code = request.args.get('code')
    shop = request.args.get('shop')
    state = request.args.get('state')
    
    # Verify state parameter
    if state != session.get('oauth_state'):
        return jsonify({"error": "Invalid state parameter"}), 400
    
    if not code or not shop:
        return jsonify({"error": "Missing authorization code or shop"}), 400
    
    try:
        # Exchange code for access token
        token_data = ShopifyOAuth.exchange_code_for_token(shop, code)
        
        # Store shop information
        session['shopify_store'] = {
            'shop': shop,
            'access_token': token_data['access_token'],
            'scope': token_data['scope'],
            'connected_at': datetime.now().isoformat(),
            'connection_id': str(uuid.uuid4())
        }
        
        # Clear OAuth state
        session.pop('oauth_state', None)
        
        return redirect(url_for('index'))
        
    except Exception as e:
        return jsonify({"error": f"OAuth failed: {str(e)}"}), 400

@app.route('/auth/disconnect')
def disconnect_oauth():
    """Disconnect OAuth app"""
    session.pop('shopify_store', None)
    return redirect(url_for('index'))

# API Routes (same as before, but using OAuth token)
@app.route('/api/store-status')
def store_status_oauth():
    """Get current store connection status (OAuth version)"""
    store_info = session.get('shopify_store')
    if store_info and isinstance(store_info, dict):
        return jsonify({
            "connected": True,
            "shop": store_info.get('shop', 'Unknown'),
            "scope": store_info.get('scope', ''),
            "connected_at": store_info.get('connected_at', ''),
            "auth_method": "oauth"
        })
    else:
        return jsonify({"connected": False})

@app.route('/api/shopify-data/<data_type>')
def get_shopify_data(data_type):
    """Get data from Shopify using OAuth token"""
    store_info = session.get('shopify_store')
    if not store_info:
        return jsonify({"error": "Not connected to Shopify"}), 401
    
    shop = store_info['shop']
    access_token = store_info['access_token']
    
    # Map data types to API endpoints
    endpoints = {
        'products': '/admin/api/2024-01/products.json',
        'orders': '/admin/api/2024-01/orders.json',
        'customers': '/admin/api/2024-01/customers.json',
        'shop': '/admin/api/2024-01/shop.json'
    }
    
    if data_type not in endpoints:
        return jsonify({"error": "Invalid data type"}), 400
    
    try:
        url = f"https://{shop}{endpoints[data_type]}"
        headers = {
            'X-Shopify-Access-Token': access_token,
            'Content-Type': 'application/json'
        }
        
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({"error": f"Shopify API error: {response.text}"}), response.status_code
            
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# Webhook endpoint
@app.route('/webhooks/<webhook_type>', methods=['POST'])
def handle_webhook(webhook_type):
    """Handle Shopify webhooks"""
    hmac_header = request.headers.get('X-Shopify-Hmac-Sha256')
    
    if not hmac_header:
        return jsonify({"error": "Missing HMAC header"}), 401
    
    if not ShopifyOAuth.verify_webhook(request.data, hmac_header):
        return jsonify({"error": "Invalid HMAC"}), 401
    
    # Process webhook data
    webhook_data = request.get_json()
    
    # Log webhook for debugging
    print(f"Received {webhook_type} webhook: {webhook_data}")
    
    # Here you can add specific webhook handling logic
    # For example, update local data when products change
    
    return jsonify({"status": "success"}), 200

# Main application routes
@app.route('/')
def index():
    """Main dashboard"""
    return render_template('index.html')

@app.route('/connect')
def connect():
    """Store connection page - redirect to OAuth install"""
    return redirect(url_for('install_app'))

@app.route('/seo')
def seo_module():
    """SEO optimization module"""
    return render_template('modules/seo.html')

@app.route('/ads')
def ads_module():
    """Advertising management module"""
    return render_template('modules/ads.html')

@app.route('/email')
def email_module():
    """Email marketing module"""
    return render_template('modules/email.html')

@app.route('/support')
def support_module():
    """Customer support module"""
    return render_template('modules/support.html')

# API Routes
@app.route('/api/ai-chat', methods=['POST'])
def ai_chat():
    """Handle AI chat requests"""
    try:
        data = request.get_json()
        message = data.get('message', '')
        module = data.get('module', 'general')

        if not message:
            return jsonify({"error": "No message provided"}), 400

        # Get AI response
        response = ai_assistant.chat(message, module)

        return jsonify({"response": response})

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/mcp-status')
def mcp_status():
    """Get MCP server status"""
    return jsonify(mcp_client.get_status())

@app.route('/api/start-mcp', methods=['POST'])
def start_mcp():
    """Start MCP server"""
    return jsonify(mcp_client.start_server())

@app.route('/api/stop-mcp', methods=['POST'])
def stop_mcp():
    """Stop MCP server"""
    return jsonify(mcp_client.stop_server())

if __name__ == '__main__':
    print("🚀 Starting Shopify AI Manager (OAuth Version)...")
    print("=" * 50)

    # Check environment variables
    if not SHOPIFY_API_KEY or not SHOPIFY_API_SECRET:
        print("⚠️  OAuth credentials not set. Running in demo mode.")
        print("To enable OAuth:")
        print("  export SHOPIFY_API_KEY='your_api_key'")
        print("  export SHOPIFY_API_SECRET='your_api_secret'")
        print("  Get these from: https://partners.shopify.com/")
    else:
        print("✅ OAuth credentials configured")

    if not OPENROUTER_API_KEY:
        print("⚠️  OPENROUTER_API_KEY not set. AI features will be limited.")
        print("  export OPENROUTER_API_KEY='your_key'")
        print("  Get your key from: https://openrouter.ai/keys")
    else:
        print("✅ OpenRouter API key configured")

    print("\n🌐 Application URLs:")
    print("  Dashboard: http://localhost:5000")
    print("  Install App: http://localhost:5000/auth/install")
    print("\n🛑 Press Ctrl+C to stop")
    print("=" * 50)

    app.run(debug=True, host='0.0.0.0', port=5000)
